using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 字符处理窗体类，提供字符标记、正则处理和干扰字符清除等功能
    /// </summary>
    /// <remarks>
    /// 主要功能：
    /// 1. 标记选中单元格中的特定值
    /// 2. 处理正则表达式匹配
    /// 3. 清除选中单元格中的干扰字符
    /// </remarks>
    public partial class frm字符处理 : Form
    {
        /// <summary>
        /// 窗体高度
        /// </summary>
        private int _formHeight;

        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        public Application XlApp;

        /// <summary>
        /// 标记选中单元格中的特定值
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void button标记_Click(object sender, EventArgs e)
        {
            try
            {
                Range selectionRange = ETExcelExtensions.GetSelectionRange();
                ValidateSelectionRange(selectionRange);

                string selectedMode = ValidateAndGetSelectedMode();

                ETExcelExtensions.SetAppFastMode();

                Dictionary<string, List<string>> valueAddressMap = selectionRange.GetValueAddressListDic();
                List<Range> rangesToHighlight = GetRangesToHighlight(valueAddressMap, selectedMode);

                // 一次性调用函数进行标色
                ETExcelExtensions.Format条件格式警示色(rangesToHighlight, EnumWarningColor.提醒);
            }
            catch (ETException ex)
            {
                MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                throw new ETException("标记处理失败", "单元格标记操作", ex);
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
            }
        }

        /// <summary>
        /// 验证选择范围是否有效
        /// </summary>
        /// <param name="selectionRange">选择的单元格范围</param>
        /// <exception cref="HyException">当选择范围无效时抛出</exception>
        private void ValidateSelectionRange(Range selectionRange)
        {
            if (selectionRange == null)
            {
                throw new ETException("请先选择要处理的单元格范围");
            }
        }

        /// <summary>
        /// 验证并获取选中的标记模式
        /// </summary>
        /// <returns>选中的标记模式</returns>
        /// <exception cref="HyException">当未选择标记模式时抛出</exception>
        private string ValidateAndGetSelectedMode()
        {
            string selectedMode = listBox标记.SelectedItem?.ToString();
            if (string.IsNullOrEmpty(selectedMode))
            {
                throw new ETException("请先选择标记模式");
            }
            return selectedMode;
        }

        /// <summary>
        /// 根据选择的模式获取需要高亮的单元格范围
        /// </summary>
        /// <param name="valueAddressMap">值和地址的映射字典</param>
        /// <param name="selectedMode">选择的标记模式</param>
        /// <returns>需要高亮的单元格范围列表</returns>
        private List<Range> GetRangesToHighlight(Dictionary<string, List<string>> valueAddressMap, string selectedMode)
        {
            List<Range> rangesToHighlight = [];

            foreach (KeyValuePair<string, List<string>> valuePair in valueAddressMap)
            {
                List<string> addresses = valuePair.Value;
                switch (selectedMode)
                {
                    case "第1个值":
                        rangesToHighlight.Add(addresses[0].GetRangeByFormulaParser());
                        break;

                    case "每个重复值:所有":
                        if (addresses.Count > 1)
                            rangesToHighlight.AddRange(addresses.Select(address => address.GetRangeByFormulaParser()));
                        break;

                    case "每个重复值:第1个":
                        if (addresses.Count > 1)
                            rangesToHighlight.Add(addresses[0].GetRangeByFormulaParser());
                        break;

                    case "每个重复值:第2个":
                        if (addresses.Count > 1)
                            rangesToHighlight.Add(addresses[1].GetRangeByFormulaParser());
                        break;

                    case "每个重复值:第2个后面部分":
                        if (addresses.Count > 1)
                            rangesToHighlight.AddRange(addresses.Skip(1).Select(address => address.GetRangeByFormulaParser()));
                        break;

                    case "最后1个值":
                        rangesToHighlight.Add(addresses[addresses.Count - 1].GetRangeByFormulaParser());
                        break;
                }
            }

            return rangesToHighlight;
        }

        /// <summary>
        /// 处理正则表达式匹配
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        public void button处理正则表达式_Click(object sender, EventArgs e)
        {
            try
            {
                Range selectionRange = ETExcelExtensions.GetSelectionRange();
                ValidateSelectionRange(selectionRange);

                string regexPattern = ValidateAndGetRegexPattern();

                ETExcelExtensions.SetAppFastMode(true);
                ProcessRegexMatchForRange(selectionRange, regexPattern);
            }
            catch (ETException ex)
            {
                MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                throw new ETException("正则表达式处理失败", "单元格内容处理", ex);
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
            }
        }

        /// <summary>
        /// 验证并获取正则表达式模式
        /// </summary>
        /// <returns>正则表达式模式</returns>
        /// <exception cref="HyException">当正则表达式为空时抛出</exception>
        private string ValidateAndGetRegexPattern()
        {
            string regexPattern = textBox正则输入框.Text;
            if (string.IsNullOrWhiteSpace(regexPattern))
            {
                throw new ETException("请输入正则表达式");
            }
            return regexPattern;
        }

        /// <summary>
        /// 对指定范围应用正则表达式匹配
        /// </summary>
        /// <param name="selectionRange">选择的单元格范围</param>
        /// <param name="regexPattern">正则表达式模式</param>
        private void ProcessRegexMatchForRange(Range selectionRange, string regexPattern)
        {
            if (regexPattern.IsNullOrWhiteSpace()) return;

            int groupIndex = string.IsNullOrWhiteSpace(textBox第几组.Text) ? 0 : Math.Max(0, int.Parse(textBox第几组.Text) - 1);

            foreach (Range cell in selectionRange.Cells)
            {
                if (cell.IsCellEmpty()) continue;

                string cellValue = cell.Value?.ToString();
                if (cellValue.IsNullOrEmpty()) continue;

                string matchResult = ETString.RegexMatchText(cellValue, regexPattern, groupIndex);
                cell.Value = matchResult ?? string.Empty;
            }
        }

        /// <summary>
        /// 清除选中单元格中的干扰字符
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        public void button去干扰字符_Click(object sender, EventArgs e)
        {
            try
            {
                Range selectedRange = ETExcelExtensions.GetSelectionRange();
                ValidateSelectionRange(selectedRange);

                ETExcelExtensions.SetAppFastMode();
                ProcessInterferenceCharacters(selectedRange);
            }
            catch (ETException ex)
            {
                MessageBox.Show(ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                throw new ETException("清除干扰字符失败", "单元格内容清理", ex);
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
            }
        }

        /// <summary>
        /// 处理单元格范围中的干扰字符
        /// </summary>
        /// <param name="selectedRange">选择的单元格范围</param>
        private void ProcessInterferenceCharacters(Range selectedRange)
        {
            foreach (Range cell in selectedRange.Cells)
            {
                if (cell.IsCellNonText()) continue;

                string cellValue = cell.Value?.ToString();
                if (cellValue.IsNullOrEmpty()) continue;

                cellValue = ApplyCharacterCleanupRules(cellValue);
                cellValue = ApplyCustomReplacements(cellValue);

                cell.Value = cellValue;
            }
        }

        /// <summary>
        /// 应用字符清理规则
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>清理后的字符串</returns>
        private string ApplyCharacterCleanupRules(string input)
        {
            if (input.IsNullOrEmpty()) return input;

            if (checkBox首尾空格.Checked) input = input.Trim();
            if (checkBox换行符.Checked) input = input.RemoveNewLines();
            if (checkBox制表符.Checked) input = input.Replace("\t", string.Empty);
            if (checkBox结尾换行符.Checked) input = input.TrimEnd();
            if (checkBoxToDBC.Checked) input = input.ToDBC().ToEn();
            if (checkBox去除所有空格.Checked) input = input.RemoveWhiteSpace();

            return input;
        }

        /// <summary>
        /// 应用自定义替换规则
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>替换后的字符串</returns>
        private string ApplyCustomReplacements(string input)
        {
            if (input.IsNullOrEmpty()) return input;

            string customRules = txt去除字符.Text.RemoveWhiteSpace();
            foreach (string line in customRules.Split('\n'))
            {
                if (line.IsNullOrWhiteSpace()) continue;

                string[] replacementParts = line.Split(new[] { "\t", "|" }, StringSplitOptions.None);
                input = input.Replace(replacementParts[0], replacementParts.Length == 1 ? string.Empty : replacementParts[1]);
            }
            return input;
        }

        /// <summary>
        /// 双击标记列表项时触发标记操作
        /// </summary>
        private void listBox标记_DoubleClick(object sender, EventArgs e)
        {
            button标记_Click(sender, e);
        }

        /// <summary>
        /// 双击内置正则表达式列表项时应用正则表达式
        /// </summary>
        private void listView内置正则表达式_DoubleClick(object sender, EventArgs e)
        {
            ListView.SelectedListViewItemCollection selectedItems = listView内置正则表达式.SelectedItems;
            if (selectedItems.Count == 0) return;

            ListViewItem selectedItem = selectedItems[0];
            textBox正则输入框.Text = selectedItem.SubItems.Count > 1 ? selectedItem.SubItems[1].Text : string.Empty;
            textBox第几组.Text = selectedItem.SubItems.Count > 2 ? selectedItem.SubItems[2].Text : string.Empty;

            button处理正则表达式_Click(sender, e);
        }

        /// <summary>
        /// 限制第几组文本框只能输入数字
        /// </summary>
        private void textBox第几组_KeyPress(object sender, KeyPressEventArgs e)
        {
            // 如果输入的不是退格和数字，则屏蔽输入
            if (!(e.KeyChar == '\b' || (e.KeyChar >= '0' && e.KeyChar <= '9')))
            {
                e.Handled = true;
            }
        }

        /// <summary>
        /// 清空去除字符文本框
        /// </summary>
        private void 清空ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            txt去除字符.Text = string.Empty;
        }

        /// <summary>
        /// 初始化字符处理窗体
        /// </summary>
        public frm字符处理()
        {
            InitializeComponent();
            XlApp = Globals.ThisAddIn.Application;
            _formHeight = Height;
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        private void frm提取字符_Load(object sender, EventArgs e)
        {
            try
            {
                XlApp = ThisAddIn.ExcelApplication;
                Dictionary<string, string[]> contextMenuDictionary = ETConfig.ConfigFileToDictionary(ETConfig.GetConfigDirectory("字符规整预置.config"));

                ETForm.LoadContextMenuStrip(contextMenuStrip替换字符, contextMenuDictionary, txt去除字符);
            }
            catch (Exception ex)
            {
                throw new ETException("窗体加载失败", "窗体初始化操作", ex);
            }
        }

        /// <summary>
        /// 标签页选择改变事件处理
        /// </summary>
        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 标签页切换时的处理逻辑（如果需要）
        }

        /// <summary>
        /// 内置正则表达式列表选择改变事件处理
        /// </summary>
        private void listView内置正则表达式_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 列表选择改变时的处理逻辑（如果需要）
        }
    }
}