﻿2025-07-27 00:30:08 [INFO] 显示设置已变更
2025-07-27 00:30:09 [INFO] 显示设置已变更
2025-07-27 00:30:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 00:30:10 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 187
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-27 00:30:10 [INFO] 显示设置变更后TopForm关系已重建
2025-07-27 00:30:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 00:30:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8787132
2025-07-27 00:30:10 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8787132)
2025-07-27 00:30:10 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-27 00:30:10 [INFO] 显示设置变更后TopForm关系已重建
2025-07-27 09:19:24 [INFO] 显示设置已变更
2025-07-27 09:19:25 [INFO] 显示设置已变更
2025-07-27 09:19:25 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 09:19:25 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 187
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-27 09:19:25 [INFO] 显示设置变更后TopForm关系已重建
2025-07-27 09:19:26 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 09:19:26 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8787132
2025-07-27 09:19:26 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8787132)
2025-07-27 09:19:26 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-27 09:19:26 [INFO] 显示设置变更后TopForm关系已重建
2025-07-27 11:18:23 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 11:18:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 8787132, 新父窗口: 4261712
2025-07-27 11:18:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4261712)
2025-07-27 11:18:23 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 11:18:23 [INFO] App_WorkbookActivate: 工作簿 '25年七期第一批已规划待实施清单-20250707(1) 的副本.xlsx' 激活处理完成
2025-07-27 11:18:23 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 11:18:23 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4261712)
2025-07-27 11:18:23 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 11:18:23 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-27 11:18:23 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 11:18:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4261712
2025-07-27 11:18:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4261712)
2025-07-27 11:18:23 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-27 11:18:23 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-27 11:18:23 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 11:18:23 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4261712
2025-07-27 11:18:23 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4261712)
2025-07-27 11:18:23 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-27 11:18:29 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-27 11:18:29 [INFO] 系统事件监控已停止
2025-07-27 11:18:29 [INFO] Excel窗口句柄监控已停止
2025-07-27 11:18:29 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-27 11:18:31 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 11:18:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8787132
2025-07-27 11:18:31 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8787132)
2025-07-27 11:18:31 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 8787132
2025-07-27 11:18:31 [INFO] 系统事件监控已启动
2025-07-27 11:18:31 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 11:18:31 [INFO] App_WorkbookActivate: 工作簿 '☆填写PPT.xlsx' 激活处理完成
2025-07-27 11:18:31 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 11:18:31 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 8787132)
2025-07-27 11:18:31 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 11:18:31 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-27 11:18:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 11:18:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8787132
2025-07-27 11:18:31 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8787132)
2025-07-27 11:18:31 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-27 11:18:31 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-27 11:18:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 11:18:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 8787132
2025-07-27 11:18:31 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 8787132)
2025-07-27 11:18:31 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-27 11:18:35 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-27 11:18:35 [INFO] 系统事件监控已停止
2025-07-27 11:18:35 [INFO] Excel窗口句柄监控已停止
2025-07-27 11:18:35 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-27 11:18:37 [INFO] 开始VSTO插件关闭流程
2025-07-27 11:18:37 [INFO] 程序集追踪日志已保存到: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\logs\AssemblyTrace_20250727_111837.txt
2025-07-27 11:18:37 [INFO] VSTO插件关闭流程完成
2025-07-27 11:18:41 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-07-27 11:18:41 [INFO] 系统事件监控已停止
2025-07-27 11:18:41 [INFO] Excel窗口句柄监控已停止
2025-07-27 11:18:41 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-07-27 11:18:42 [INFO] 开始VSTO插件关闭流程
2025-07-27 11:18:42 [INFO] 程序集追踪日志已保存到: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\logs\AssemblyTrace_20250727_111842.txt
2025-07-27 11:18:42 [INFO] VSTO插件关闭流程完成
2025-07-27 13:31:09 [INFO] Excel窗口句柄监控器初始化完成
2025-07-27 13:31:09 [INFO] 配置文件实例已在加载时初始化
2025-07-27 13:31:09 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-07-27 13:31:09 [INFO] 🔍 === 直接测试znAbout控件状态 ===
2025-07-27 13:31:09 [INFO] 🔍 znAbout控件实例: 存在
2025-07-27 13:31:09 [INFO] 🔍 znAbout.Label: 'ZnAbout'
2025-07-27 13:31:09 [INFO] 🔍 znAbout.Name: 'znAbout'
2025-07-27 13:31:09 [INFO] 🔍 znAbout类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-27 13:31:09 [INFO] 🔍 znAboutGroup控件实例: 存在
2025-07-27 13:31:09 [INFO] 🔍 znAboutGroup.Label: '授权'
2025-07-27 13:31:09 [INFO] 🔍 znAboutGroup.Name: 'znAboutGroup'
2025-07-27 13:31:09 [INFO] 🔍 znAboutGroup类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-27 13:31:09 [INFO] 🔍 znAboutButton控件实例: 存在
2025-07-27 13:31:09 [INFO] 🔍 znAboutButton.Label: '授权'
2025-07-27 13:31:09 [INFO] 🔍 znAboutButton.Name: 'znAboutButton'
2025-07-27 13:31:09 [INFO] 🔍 znAboutButton类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-27 13:31:09 [INFO] 🔍 === znAbout控件状态测试完成 ===
2025-07-27 13:31:09 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-07-27 13:31:09 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-07-27 13:31:09 [INFO] 成功初始化Excel应用程序实例
2025-07-27 13:31:09 [INFO] 自动备份路径未配置
2025-07-27 13:31:10 [DEBUG] 开始初始化授权控制器
2025-07-27 13:31:10 [DEBUG] 授权系统初始化完成，耗时: 664ms
2025-07-27 13:31:10 [DEBUG] 开始初始化授权验证
2025-07-27 13:31:10 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-07-27 13:31:10 [DEBUG] 权限管理器初始化成功
2025-07-27 13:31:10 [DEBUG] 使用新的权限管理器进行初始化
2025-07-27 13:31:10 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-27 13:31:10 [INFO] 开始初始化UI权限管理
2025-07-27 13:31:10 [DEBUG] [实例ID: 189df0ed] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-27 13:31:10 [DEBUG] 🔍 [实例ID: 189df0ed] 字典引用一致性检查:
2025-07-27 13:31:10 [DEBUG] 🔍   标题映射一致性: True
2025-07-27 13:31:10 [DEBUG] 🔍   权限映射一致性: True
2025-07-27 13:31:10 [DEBUG] 🔍   信息映射一致性: True
2025-07-27 13:31:10 [DEBUG] 🔍   特殊控件一致性: True
2025-07-27 13:31:10 [DEBUG] 控件权限管理器初始化完成 [实例ID: 189df0ed]
2025-07-27 13:31:10 [DEBUG] 开始注册控件权限映射
2025-07-27 13:31:10 [INFO] 开始初始化全局控件映射
2025-07-27 13:31:10 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-07-27 13:31:10 [DEBUG] 开始生成控件标题映射
2025-07-27 13:31:10 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-27 13:31:10 [DEBUG] 通过反射获取到 112 个字段
2025-07-27 13:31:10 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:10 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-27 13:31:10 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-27 13:31:10 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-27 13:31:10 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-27 13:31:10 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-27 13:31:10 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:10 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-27 13:31:10 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-27 13:31:10 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-07-27 13:31:10 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-07-27 13:31:10 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-07-27 13:31:10 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-07-27 13:31:10 [INFO] 控件标题映射生成完成，共生成 100 项映射
2025-07-27 13:31:10 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-27 13:31:10 [DEBUG] 全局控件标题映射生成完成，共生成 100 项
2025-07-27 13:31:10 [INFO] 关键控件标题映射: hyTab -> Develop
2025-07-27 13:31:10 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-07-27 13:31:10 [WARN] 关键控件未找到标题映射: buttonAbout
2025-07-27 13:31:10 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-07-27 13:31:10 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-07-27 13:31:11 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-07-27 13:31:11 [INFO] === znAbout控件标题映射诊断 ===
2025-07-27 13:31:11 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-07-27 13:31:11 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-07-27 13:31:11 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-07-27 13:31:11 [DEBUG] === 所有生成的控件标题映射 ===
2025-07-27 13:31:11 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn发送及存档 -> '发送及存档'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规检查'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-07-27 13:31:11 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-07-27 13:31:11 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-07-27 13:31:11 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-07-27 13:31:11 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-07-27 13:31:11 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-07-27 13:31:11 [DEBUG] 控件映射: button14 -> '发送及存档'
2025-07-27 13:31:11 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-07-27 13:31:11 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-07-27 13:31:11 [DEBUG] 控件映射: button17 -> '向下填充'
2025-07-27 13:31:11 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-07-27 13:31:11 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-07-27 13:31:11 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-07-27 13:31:11 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-07-27 13:31:11 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-07-27 13:31:11 [DEBUG] 控件映射: button3 -> '关于'
2025-07-27 13:31:11 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-07-27 13:31:11 [DEBUG] 控件映射: button5 -> '文件管理'
2025-07-27 13:31:11 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-07-27 13:31:11 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-07-27 13:31:11 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-07-27 13:31:11 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-07-27 13:31:11 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-07-27 13:31:11 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-07-27 13:31:11 [DEBUG] 控件映射: button9 -> '文件快开'
2025-07-27 13:31:11 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-07-27 13:31:11 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-07-27 13:31:11 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-07-27 13:31:11 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-07-27 13:31:11 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-07-27 13:31:11 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-07-27 13:31:11 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-07-27 13:31:11 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-07-27 13:31:11 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-07-27 13:31:11 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-07-27 13:31:11 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-07-27 13:31:11 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-07-27 13:31:11 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-07-27 13:31:11 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-07-27 13:31:11 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-07-27 13:31:11 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-07-27 13:31:11 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-07-27 13:31:11 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-07-27 13:31:11 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-07-27 13:31:11 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-07-27 13:31:11 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-07-27 13:31:11 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-07-27 13:31:11 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-07-27 13:31:11 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-07-27 13:31:11 [DEBUG] 控件映射: button文件操作 -> '文件操作'
2025-07-27 13:31:11 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-07-27 13:31:11 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-07-27 13:31:11 [DEBUG] 控件映射: button专用工具 -> '专用工具'
2025-07-27 13:31:11 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-07-27 13:31:11 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-07-27 13:31:11 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-07-27 13:31:11 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-07-27 13:31:11 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-07-27 13:31:11 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-07-27 13:31:11 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-07-27 13:31:11 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-07-27 13:31:11 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-07-27 13:31:11 [DEBUG] 控件映射: group1 -> '关于'
2025-07-27 13:31:11 [DEBUG] 控件映射: group2 -> '脚本'
2025-07-27 13:31:11 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-07-27 13:31:11 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-07-27 13:31:11 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-07-27 13:31:11 [DEBUG] 控件映射: group文件 -> '文件'
2025-07-27 13:31:11 [DEBUG] 控件映射: group无线 -> '无线'
2025-07-27 13:31:11 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-07-27 13:31:11 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-07-27 13:31:11 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-07-27 13:31:11 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-07-27 13:31:11 [DEBUG] 控件映射: menu1 -> '其它'
2025-07-27 13:31:11 [DEBUG] 控件映射: menu3 -> '设置'
2025-07-27 13:31:11 [DEBUG] 控件映射: menu5 -> '修复'
2025-07-27 13:31:11 [DEBUG] 控件映射: menuHY -> '其它'
2025-07-27 13:31:11 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-07-27 13:31:11 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-07-27 13:31:11 [DEBUG] 控件映射: menu修复 -> '修复'
2025-07-27 13:31:11 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-07-27 13:31:11 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-07-27 13:31:11 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-07-27 13:31:11 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-07-27 13:31:11 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-07-27 13:31:11 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-07-27 13:31:11 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-07-27 13:31:11 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-07-27 13:31:11 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-07-27 13:31:11 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-07-27 13:31:11 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-07-27 13:31:11 [DEBUG] 开始生成控件权限映射
2025-07-27 13:31:11 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-07-27 13:31:11 [DEBUG] 通过反射获取到 112 个字段
2025-07-27 13:31:11 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-07-27 13:31:11 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-07-27 13:31:11 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-07-27 13:31:11 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-07-27 13:31:11 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-27 13:31:11 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-07-27 13:31:11 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-07-27 13:31:11 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-07-27 13:31:11 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-07-27 13:31:11 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-07-27 13:31:11 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-07-27 13:31:11 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-07-27 13:31:11 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-07-27 13:31:11 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-07-27 13:31:11 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-07-27 13:31:11 [INFO] 控件结构获取完成，共获取到 110 个控件
2025-07-27 13:31:11 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-07-27 13:31:11 [INFO] 控件权限映射生成完成，共生成 104 项映射
2025-07-27 13:31:11 [DEBUG] 全局控件权限映射生成完成，共生成 104 项
2025-07-27 13:31:11 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-07-27 13:31:11 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-07-27 13:31:11 [INFO] 全局控件映射初始化完成 - 标题映射: 100 项, 权限映射: 104 项
2025-07-27 13:31:11 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-27 13:31:11 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-27 13:31:11 [INFO] 开始初始化权限验证
2025-07-27 13:31:11 [DEBUG] 设置默认UI可见性为false
2025-07-27 13:31:11 [DEBUG] 开始检查所有需要的权限
2025-07-27 13:31:11 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-07-27 13:31:12 [INFO] 启动网络授权信息获取任务
2025-07-27 13:31:12 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-07-27 13:31:12 [INFO] 所有权限检查完成
2025-07-27 13:31:12 [DEBUG] 应用权限状态到UI控件
2025-07-27 13:31:12 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-27 13:31:12 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-27 13:31:12 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:12 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-27 13:31:12 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-27 13:31:12 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:12 [DEBUG] 已应用权限状态到UI控件
2025-07-27 13:31:12 [DEBUG] 启动后台权限刷新任务
2025-07-27 13:31:12 [DEBUG] 启动延迟权限刷新任务
2025-07-27 13:31:12 [INFO] 权限验证初始化完成
2025-07-27 13:31:12 [INFO] UI权限管理初始化完成
2025-07-27 13:31:12 [INFO] 收到权限管理器初始化完成通知
2025-07-27 13:31:12 [INFO] 开始刷新控件标题
2025-07-27 13:31:12 [DEBUG] 开始刷新所有控件权限状态
2025-07-27 13:31:12 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-27 13:31:12 [DEBUG] 控件标题刷新完成
2025-07-27 13:31:12 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-27 13:31:12 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-27 13:31:12 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-27 13:31:12 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-27 13:31:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-27 13:31:12 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-27 13:31:12 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-27 13:31:12 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:12 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-27 13:31:12 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-27 13:31:12 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-27 13:31:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-27 13:31:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-27 13:31:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-27 13:31:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-27 13:31:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-27 13:31:12 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-27 13:31:12 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-27 13:31:12 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-27 13:31:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-27 13:31:12 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-27 13:31:12 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-27 13:31:12 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-27 13:31:12 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-27 13:31:12 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-27 13:31:12 [INFO] 控件标题更正完成
2025-07-27 13:31:12 [INFO] 控件标题刷新完成
2025-07-27 13:31:12 [INFO] 权限管理器初始化完成处理结束
2025-07-27 13:31:12 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-27 13:31:12 [DEBUG] 授权验证初始化完成
2025-07-27 13:31:12 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\hyExcelDnaData.xlsx
2025-07-27 13:31:12 [INFO] 成功加载配置和授权信息
2025-07-27 13:31:12 [INFO] 开始初始化定时器和设置
2025-07-27 13:31:12 [INFO] 定时器和设置初始化完成
2025-07-27 13:31:12 [INFO] 开始VSTO插件启动流程
2025-07-27 13:31:12 [INFO] TopMostForm窗体加载完成
2025-07-27 13:31:12 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 13:31:12 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3150950
2025-07-27 13:31:12 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3150950)
2025-07-27 13:31:12 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 3150950
2025-07-27 13:31:12 [INFO] 系统事件监控已启动
2025-07-27 13:31:12 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 13:31:12 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-07-27 13:31:12 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-07-27 13:31:12 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-07-27 13:31:12 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 2821908
2025-07-27 13:31:12 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-07-27 13:31:12 [INFO] VSTO插件启动流程完成
2025-07-27 13:31:12 [DEBUG] 开始重置 209 个命令栏
2025-07-27 13:31:13 [INFO] 从Remote成功获取到网络授权信息
2025-07-27 13:31:13 [INFO] 网络授权信息已更新并触发回调
2025-07-27 13:31:13 [INFO] 网络授权信息已从 Network 更新
2025-07-27 13:31:13 [INFO] 授权版本: 1.0
2025-07-27 13:31:13 [INFO] 颁发者: ExtensionsTools
2025-07-27 13:31:13 [INFO] 用户数量: 2
2025-07-27 13:31:13 [INFO] 分组权限数量: 2
2025-07-27 13:31:13 [WARN] 配置文件中未找到用户组信息
2025-07-27 13:31:13 [INFO] 已重新设置用户组: []
2025-07-27 13:31:13 [INFO] 用户组信息已重新设置
2025-07-27 13:31:13 [INFO] 立即刷新权限缓存和UI界面
2025-07-27 13:31:13 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-27 13:31:13 [DEBUG] 使用新的权限管理器进行强制刷新
2025-07-27 13:31:13 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-07-27 13:31:13 [INFO] 开始强制刷新权限缓存和UI界面
2025-07-27 13:31:13 [DEBUG] 本地权限缓存已清空
2025-07-27 13:31:13 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-07-27 13:31:13 [INFO] 所有权限检查完成
2025-07-27 13:31:13 [DEBUG] 权限重新检查完成
2025-07-27 13:31:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-27 13:31:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:13 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-27 13:31:13 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:13 [DEBUG] 已应用权限状态到UI控件
2025-07-27 13:31:13 [INFO] UI界面权限状态已更新
2025-07-27 13:31:13 [DEBUG] 开始刷新所有控件权限状态
2025-07-27 13:31:13 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-27 13:31:13 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-07-27 13:31:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-27 13:31:13 [INFO] 权限缓存和UI界面立即刷新完成
2025-07-27 13:31:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:13 [INFO] 网络授权已更新，开始刷新控件标题
2025-07-27 13:31:13 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-27 13:31:13 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:13 [DEBUG] 已应用权限状态到UI控件
2025-07-27 13:31:13 [INFO] 开始刷新Ribbon控件标题
2025-07-27 13:31:13 [DEBUG] 权限缓存已清空，清除了 104 个缓存项
2025-07-27 13:31:13 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-07-27 13:31:13 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-27 13:31:13 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-27 13:31:13 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-27 13:31:13 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-27 13:31:13 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-27 13:31:13 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:13 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-27 13:31:13 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-27 13:31:13 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-27 13:31:13 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-27 13:31:13 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-27 13:31:13 [INFO] 控件标题更正完成
2025-07-27 13:31:13 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-07-27 13:31:13 [INFO] Ribbon控件标题刷新完成
2025-07-27 13:31:13 [INFO] 控件标题刷新完成
2025-07-27 13:31:13 [DEBUG] Ribbon控件标题已刷新
2025-07-27 13:31:13 [INFO] 开始刷新控件标题
2025-07-27 13:31:13 [DEBUG] 开始刷新所有控件权限状态
2025-07-27 13:31:13 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-27 13:31:13 [DEBUG] 控件标题刷新完成
2025-07-27 13:31:13 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-27 13:31:13 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-27 13:31:13 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-27 13:31:13 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-27 13:31:13 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-27 13:31:13 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:13 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-27 13:31:13 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-27 13:31:13 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-27 13:31:13 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-27 13:31:13 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-27 13:31:13 [INFO] 控件标题更正完成
2025-07-27 13:31:13 [INFO] 控件标题刷新完成
2025-07-27 13:31:13 [DEBUG] Ribbon控件标题已立即刷新
2025-07-27 13:31:13 [DEBUG] 重置命令栏: cell
2025-07-27 13:31:13 [DEBUG] 重置命令栏: column
2025-07-27 13:31:13 [DEBUG] 重置命令栏: row
2025-07-27 13:31:13 [DEBUG] 重置命令栏: cell
2025-07-27 13:31:13 [DEBUG] 重置命令栏: column
2025-07-27 13:31:13 [DEBUG] 重置命令栏: row
2025-07-27 13:31:13 [DEBUG] 重置命令栏: row
2025-07-27 13:31:13 [DEBUG] 重置命令栏: column
2025-07-27 13:31:13 [INFO] 开始刷新授权状态
2025-07-27 13:31:13 [DEBUG] 开始初始化授权验证
2025-07-27 13:31:13 [DEBUG] 使用新的权限管理器进行初始化
2025-07-27 13:31:13 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-07-27 13:31:13 [INFO] 开始初始化UI权限管理
2025-07-27 13:31:13 [DEBUG] [实例ID: 72266923] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-07-27 13:31:13 [DEBUG] 🔍 [实例ID: 72266923] 字典引用一致性检查:
2025-07-27 13:31:13 [DEBUG] 🔍   标题映射一致性: True
2025-07-27 13:31:13 [DEBUG] 🔍   权限映射一致性: True
2025-07-27 13:31:13 [DEBUG] 🔍   信息映射一致性: True
2025-07-27 13:31:13 [DEBUG] 🔍   特殊控件一致性: True
2025-07-27 13:31:13 [DEBUG] 控件权限管理器初始化完成 [实例ID: 72266923]
2025-07-27 13:31:13 [DEBUG] 开始注册控件权限映射
2025-07-27 13:31:13 [DEBUG] 批量注册控件权限映射完成，成功: 104/104
2025-07-27 13:31:13 [DEBUG] HyExcel控件权限映射注册完成，共注册 104 个控件
2025-07-27 13:31:13 [INFO] 开始初始化权限验证
2025-07-27 13:31:13 [DEBUG] 设置默认UI可见性为false
2025-07-27 13:31:13 [DEBUG] 开始检查所有需要的权限
2025-07-27 13:31:13 [INFO] 所有权限检查完成
2025-07-27 13:31:13 [DEBUG] 应用权限状态到UI控件
2025-07-27 13:31:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-27 13:31:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:13 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-27 13:31:13 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:13 [DEBUG] 已应用权限状态到UI控件
2025-07-27 13:31:13 [DEBUG] 启动后台权限刷新任务
2025-07-27 13:31:13 [DEBUG] 启动延迟权限刷新任务
2025-07-27 13:31:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-27 13:31:13 [INFO] 权限验证初始化完成
2025-07-27 13:31:13 [INFO] UI权限管理初始化完成
2025-07-27 13:31:13 [INFO] 收到权限管理器初始化完成通知
2025-07-27 13:31:13 [INFO] 开始刷新控件标题
2025-07-27 13:31:13 [DEBUG] 开始刷新所有控件权限状态
2025-07-27 13:31:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-27 13:31:13 [DEBUG] 控件权限状态刷新完成，已检查 104 个控件
2025-07-27 13:31:13 [DEBUG] 控件标题刷新完成
2025-07-27 13:31:13 [INFO] 开始动态更正控件标题（避免硬编码）
2025-07-27 13:31:13 [DEBUG] 开始动态获取Ribbon控件引用
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:13 [DEBUG] 🔍 HyRibbon反射获取到 112 个字段
2025-07-27 13:31:13 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-07-27 13:31:13 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-07-27 13:31:13 [DEBUG] 已应用权限状态到UI控件
2025-07-27 13:31:13 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-07-27 13:31:13 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-07-27 13:31:13 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-07-27 13:31:13 [INFO] 动态获取到 110 个Ribbon控件引用
2025-07-27 13:31:13 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-07-27 13:31:13 [INFO] 开始批量更新控件标题，共 110 个控件
2025-07-27 13:31:13 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-07-27 13:31:13 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-07-27 13:31:13 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-07-27 13:31:13 [INFO] 批量更新控件标题完成，成功更新 100 个控件
2025-07-27 13:31:13 [INFO] 动态批量更新完成，共更新 110 个控件
2025-07-27 13:31:13 [INFO] 控件标题更正完成
2025-07-27 13:31:13 [INFO] 控件标题刷新完成
2025-07-27 13:31:13 [INFO] 权限管理器初始化完成处理结束
2025-07-27 13:31:13 [DEBUG] HyExcel UI权限管理器初始化完成
2025-07-27 13:31:13 [DEBUG] 授权验证初始化完成
2025-07-27 13:31:13 [INFO] 授权状态刷新完成
2025-07-27 13:31:13 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-07-27 13:31:14 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-27 13:31:14 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-27 13:31:14 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:14 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-27 13:31:14 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-27 13:31:14 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:14 [DEBUG] 已应用权限状态到UI控件
2025-07-27 13:31:15 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:31:15 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 3150950
2025-07-27 13:31:15 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 3150950)
2025-07-27 13:31:15 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-27 13:31:15 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-27 13:31:15 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-27 13:31:15 [DEBUG] 授权控制器已初始化
2025-07-27 13:31:15 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-27 13:31:15 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-07-27 13:31:15 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-07-27 13:31:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:15 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-07-27 13:31:15 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-07-27 13:31:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-07-27 13:31:15 [DEBUG] 已应用权限状态到UI控件
2025-07-27 13:31:16 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-27 13:31:16 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-27 13:31:16 [DEBUG] 授权控制器已初始化
2025-07-27 13:31:16 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-27 13:31:16 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-27 13:31:17 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-27 13:31:17 [DEBUG] 授权控制器已初始化
2025-07-27 13:31:17 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-27 13:31:17 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-27 13:31:17 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-27 13:31:17 [DEBUG] 授权控制器已初始化
2025-07-27 13:31:17 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-27 13:31:18 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-27 13:31:18 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-27 13:31:18 [DEBUG] 授权控制器已初始化
2025-07-27 13:31:18 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-27 13:31:18 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-27 13:31:18 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-27 13:31:18 [DEBUG] 授权控制器已初始化
2025-07-27 13:31:18 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-27 13:31:19 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-27 13:31:19 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-27 13:31:19 [DEBUG] 授权控制器已初始化
2025-07-27 13:31:19 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-27 13:31:19 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-27 13:31:19 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-27 13:31:19 [DEBUG] 授权控制器已初始化
2025-07-27 13:31:19 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-07-27 13:31:20 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-07-27 13:31:20 [DEBUG] 已重置工作表标签菜单
2025-07-27 13:31:20 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-07-27 13:32:31 [DEBUG] 复选框初始化完成
2025-07-27 13:32:31 [INFO] 多边形GPS坐标转换器窗体初始化完成
2025-07-27 13:32:31 [INFO] OpenForm: 准备打开窗体 'PolygonGpsConverter'，位置: Center，单实例: True
2025-07-27 13:32:31 [INFO] 开始显示窗体 'PolygonGpsConverter'，位置模式: Center
2025-07-27 13:32:31 [INFO] 多边形GPS坐标转换器窗体加载完成
2025-07-27 13:32:31 [INFO] 窗体 'PolygonGpsConverter' 以TopMostForm为父窗体显示
2025-07-27 13:32:31 [INFO] 窗体 'PolygonGpsConverter' 显示完成，句柄: 1054422
2025-07-27 13:32:31 [INFO] OpenForm: 窗体 'PolygonGpsConverter' 打开成功
2025-07-27 13:32:47 [INFO] 多边形GPS坐标转换器窗体正在关闭
2025-07-27 13:32:49 [DEBUG] 复选框初始化完成
2025-07-27 13:32:49 [INFO] 多边形GPS坐标转换器窗体初始化完成
2025-07-27 13:32:49 [INFO] OpenForm: 准备打开窗体 'PolygonGpsConverter'，位置: Center，单实例: True
2025-07-27 13:32:49 [INFO] 开始显示窗体 'PolygonGpsConverter'，位置模式: Center
2025-07-27 13:32:49 [INFO] 多边形GPS坐标转换器窗体加载完成
2025-07-27 13:32:49 [INFO] 窗体 'PolygonGpsConverter' 以TopMostForm为父窗体显示
2025-07-27 13:32:49 [INFO] 窗体 'PolygonGpsConverter' 显示完成，句柄: 2955522
2025-07-27 13:32:49 [INFO] OpenForm: 窗体 'PolygonGpsConverter' 打开成功
2025-07-27 13:32:54 [INFO] 多边形GPS坐标转换器窗体正在关闭
2025-07-27 13:32:57 [DEBUG] 复选框初始化完成
2025-07-27 13:32:57 [INFO] 多边形GPS坐标转换器窗体初始化完成
2025-07-27 13:32:57 [INFO] OpenForm: 准备打开窗体 'PolygonGpsConverter'，位置: Center，单实例: True
2025-07-27 13:32:57 [INFO] 开始显示窗体 'PolygonGpsConverter'，位置模式: Center
2025-07-27 13:32:57 [INFO] 多边形GPS坐标转换器窗体加载完成
2025-07-27 13:32:57 [INFO] 窗体 'PolygonGpsConverter' 以TopMostForm为父窗体显示
2025-07-27 13:32:57 [INFO] 窗体 'PolygonGpsConverter' 显示完成，句柄: 3020956
2025-07-27 13:32:57 [INFO] OpenForm: 窗体 'PolygonGpsConverter' 打开成功
2025-07-27 13:33:04 [INFO] 多边形GPS坐标转换器窗体正在关闭
2025-07-27 13:47:33 [INFO] App_WorkbookOpen: 工作簿 '☆51交付管理.xlsx' 打开事件触发
2025-07-27 13:47:33 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 13:47:33 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 2299722
2025-07-27 13:47:33 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 2299722)
2025-07-27 13:47:33 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 13:47:33 [INFO] App_WorkbookOpen: 工作簿 '☆51交付管理.xlsx' 打开处理完成
2025-07-27 13:47:33 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 13:47:33 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 2299722)
2025-07-27 13:47:33 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 13:47:33 [INFO] App_WorkbookActivate: 工作簿 '☆51交付管理.xlsx' 激活处理完成
2025-07-27 13:47:33 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 13:47:33 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 2299722)
2025-07-27 13:47:33 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 13:47:33 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-27 13:47:33 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:47:33 [WARN] 检测到Excel窗口句柄变化: 3150950 -> 2299722
2025-07-27 13:47:33 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 2299722)
2025-07-27 13:47:33 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 187
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-27 13:47:33 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-27 13:47:33 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:47:33 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 187
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-27 13:47:33 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:47:33 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 187
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-27 13:47:33 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 13:47:54 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 2299722)
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 13:47:54 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 13:47:54 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 2299722)
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 13:47:54 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 13:47:54 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 2299722, 新父窗口: 1578652
2025-07-27 13:47:54 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1578652)
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 13:47:54 [INFO] App_WorkbookActivate: 工作簿 '☆51交付管理.xlsx' 激活处理完成
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 13:47:54 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1578652)
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 13:47:54 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-27 13:47:54 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 13:47:54 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1578652)
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 13:47:54 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 187
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-27 13:47:54 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-07-27 13:47:54 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-07-27 13:47:54 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1578652)
2025-07-27 13:47:54 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-07-27 13:47:54 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-07-27 13:47:54 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:47:54 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 187
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-27 13:47:54 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:47:54 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 187
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-27 13:47:54 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-27 13:47:54 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:47:54 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 187
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-27 13:47:54 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-07-27 13:47:54 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:47:54 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 187
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-27 13:47:55 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:47:55 [ERROR] TopMostForm.ForceRestoreParentRelation: 强制恢复失败 - 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
异常详情：System.InvalidOperationException: 线程间操作无效: 从不是创建控件“frmTopForm”的线程访问它。
   在 System.Windows.Forms.Control.get_Handle()
   在 ET.ETForm.AsChildWindow(Form form, IntPtr intPtr) 位置 D:\HyDevelop\HyHelper\HyHelper\ExtensionsTools\ExtensionsTools\ETForm.cs:行号 187
   在 HyExcelVsto.Module.Common.frmTopMostForm.ForceRestoreParentRelation() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\frmTopForm.cs:行号 680
2025-07-27 13:47:57 [WARN] 检测到Excel窗口句柄变化: 2299722 -> 1578652
2025-07-27 13:47:57 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 1578652)
2025-07-27 13:48:20 [INFO] OpenForm: 准备打开窗体 '字符处理'，位置: Right，单实例: True
2025-07-27 13:48:20 [INFO] 开始显示窗体 '字符处理'，位置模式: Right
2025-07-27 13:48:20 [INFO] 窗体 '字符处理' 以TopMostForm为父窗体显示
2025-07-27 13:48:20 [INFO] 窗体 '字符处理' 显示完成，句柄: 11079250
2025-07-27 13:48:20 [INFO] OpenForm: 窗体 '字符处理' 打开成功
2025-07-27 13:49:14 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:49:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1578652
2025-07-27 13:49:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1578652)
2025-07-27 13:49:14 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-27 13:49:14 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-07-27 13:49:28 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-07-27 13:49:28 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1578652
2025-07-27 13:49:28 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1578652)
2025-07-27 13:49:28 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-07-27 13:49:28 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
